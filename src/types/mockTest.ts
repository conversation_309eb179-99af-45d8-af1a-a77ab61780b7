import { Subject } from "../components/productivity/SubjectManager";

export interface SubjectMarks {
  subject: string;
  subjectColor?: string;
  marksObtained: number;
  totalMarks: number;
}

// Enhanced interfaces for new features
export interface TestCategory {
  id: string;
  name: string;
  description?: string;
  color?: string;
  userId: string;
  createdAt: string;
}

export interface ChapterProgress {
  chapterId: string;
  chapterName: string;
  subject: string;
  isCompleted: boolean;
  completedAt?: string;
  notes?: string;
}

export interface TestSyllabus {
  testId: string;
  chapters: ChapterProgress[];
  overallProgress: number; // Percentage of completed chapters
  lastUpdated: string;
}

export interface TestMistake {
  id: string;
  subject: string;
  topic: string;
  description: string;
  solution?: string;
  tags?: string[];
  createdAt: string;
}

export interface TestTakeaway {
  id: string;
  category: 'strength' | 'weakness' | 'strategy' | 'concept' | 'other';
  subject?: string;
  description: string;
  actionPlan?: string;
  priority: 'high' | 'medium' | 'low';
  createdAt: string;
}

export interface UpcomingTest {
  id: string;
  name: string;
  date: string; // YYYY-MM-DD format
  time?: string; // HH:MM format
  categoryId?: string;
  syllabus?: string[];
  testPaperUrl?: string;
  isNotificationEnabled: boolean;
  daysLeft?: number;
  userId: string;
  createdAt: string;
}

// Enhanced MockTest interface with new features
export interface MockTest {
  id: string;          // Unique ID for the mock test
  name: string;        // Name of the mock test
  date: string;        // Date of the test in YYYY-MM-DD format
  subjectMarks: SubjectMarks[]; // Array of subjects with their marks
  totalMarksObtained: number; // Total marks obtained across all subjects
  totalMarks: number;  // Total marks across all subjects
  notes?: string;      // Optional notes about the test
  createdAt: string;   // Timestamp when the test was created
  userId: string;      // User ID who created the test

  // Enhanced fields (stored locally)
  categoryId?: string; // Reference to TestCategory
  testPaperUrl?: string; // URL to test paper PDF
  isReviewed: boolean; // Whether the test has been reviewed/analyzed
  reviewedAt?: string; // When the test was reviewed
  mistakes: TestMistake[]; // Array of mistakes made in this test
  takeaways: TestTakeaway[]; // Array of key takeaways from this test
  difficulty: 'easy' | 'medium' | 'hard' | 'very_hard'; // Test difficulty level
  timeSpent?: number; // Time spent on test in minutes
  targetScore?: number; // Target score for this test
  isFromUpcoming?: boolean; // Whether this test was created from upcoming tests
}

export interface MockTestAnalytics {
  totalTests: number;
  averageScore: number;
  highestScore: {
    testId: string;
    testName: string;
    score: number;
    percentage: number;
  };
  lowestScore: {
    testId: string;
    testName: string;
    score: number;
    percentage: number;
  };
  subjectPerformance: {
    [subject: string]: {
      totalTests: number;
      averageScore: number;
      averagePercentage: number;
    }
  };
  recentTests: MockTest[];

  // Enhanced analytics
  categoryPerformance: {
    [categoryId: string]: {
      categoryName: string;
      totalTests: number;
      averageScore: number;
      averagePercentage: number;
      trend: 'improving' | 'declining' | 'stable';
    }
  };
  performanceTrend: {
    date: string;
    score: number;
    percentage: number;
    testName: string;
    categoryId?: string;
  }[];
  reviewedTestsCount: number;
  unreviewed TestsCount: number;
  mistakesBySubject: {
    [subject: string]: number;
  };
  strengthsBySubject: {
    [subject: string]: number;
  };
}