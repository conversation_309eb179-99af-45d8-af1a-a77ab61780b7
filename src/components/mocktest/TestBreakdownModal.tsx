import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { 
  X, 
  Calendar, 
  Clock, 
  Target, 
  BookOpen, 
  FileText, 
  ExternalLink,
  Edit2,
  Save,
  Plus,
  Trash2,
  CheckCircle2,
  AlertCircle,
  Award,
  TrendingUp,
  BarChart3
} from "lucide-react";
import { 
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { 
  Ta<PERSON>,
  Ta<PERSON>Content,
  <PERSON><PERSON><PERSON><PERSON>,
  TabsTrigger,
} from "@/components/ui/tabs";
import { MockTest, TestMistake, TestTakeaway, TestSyllabus } from "@/types/mockTest";
import {
  enhancedMockTestUtils,
  mistakesStorage,
  takeawaysStorage,
  syllabusStorage,
  urlUtils
} from "@/utils/mockTestLocalStorage";
import { SyllabusTracker } from "./SyllabusTracker";
import { toast } from "@/components/ui/use-toast";
import { cn } from "@/lib/utils";

interface TestBreakdownModalProps {
  test: MockTest | null;
  isOpen: boolean;
  onClose: () => void;
  onTestUpdate?: (updatedTest: MockTest) => void;
}

export function TestBreakdownModal({ test, isOpen, onClose, onTestUpdate }: TestBreakdownModalProps) {
  const [mistakes, setMistakes] = useState<TestMistake[]>([]);
  const [takeaways, setTakeaways] = useState<TestTakeaway[]>([]);
  const [syllabus, setSyllabus] = useState<TestSyllabus | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  
  // Form states for editing
  const [editForm, setEditForm] = useState({
    marksObtained: 0,
    totalMarks: 0,
    timeSpent: 0,
    difficulty: 'medium' as const,
    targetScore: 0,
    isReviewed: false,
  });

  // Mistake form
  const [mistakeForm, setMistakeForm] = useState({
    subject: "",
    topic: "",
    description: "",
    solution: "",
  });

  // Takeaway form
  const [takeawayForm, setTakeawayForm] = useState({
    category: "",
    description: "",
  });

  const [isAddingMistake, setIsAddingMistake] = useState(false);
  const [isAddingTakeaway, setIsAddingTakeaway] = useState(false);

  // Load data when test changes
  useEffect(() => {
    if (test) {
      loadTestData();
      setEditForm({
        marksObtained: test.marksObtained || 0,
        totalMarks: test.totalMarks || 0,
        timeSpent: test.timeSpent || 0,
        difficulty: test.difficulty || 'medium',
        targetScore: test.targetScore || 0,
        isReviewed: test.isReviewed || false,
      });
    }
  }, [test]);

  const loadTestData = () => {
    if (!test) return;
    
    const testMistakes = mistakesStorage.getByTest(test.id);
    const testTakeaways = takeawaysStorage.getByTest(test.id);
    const testSyllabus = syllabusStorage.get(test.id);
    
    setMistakes(testMistakes);
    setTakeaways(testTakeaways);
    setSyllabus(testSyllabus);
  };

  const handleSaveEdit = () => {
    if (!test) return;

    const updatedFields = {
      marksObtained: editForm.marksObtained,
      totalMarks: editForm.totalMarks,
      timeSpent: editForm.timeSpent,
      difficulty: editForm.difficulty,
      targetScore: editForm.targetScore,
      isReviewed: editForm.isReviewed,
      reviewedAt: editForm.isReviewed ? new Date().toISOString() : undefined,
    };

    enhancedMockTestUtils.saveEnhancedFields(test.id, updatedFields);
    
    const updatedTest = { ...test, ...updatedFields };
    onTestUpdate?.(updatedTest);
    setIsEditing(false);

    toast({
      title: "Success",
      description: "Test details updated successfully",
    });
  };

  const handleAddMistake = () => {
    if (!test || !mistakeForm.subject || !mistakeForm.description) {
      toast({
        title: "Error",
        description: "Please fill in required fields",
        variant: "destructive",
      });
      return;
    }

    const newMistake = mistakesStorage.add(test.id, {
      subject: mistakeForm.subject,
      topic: mistakeForm.topic,
      description: mistakeForm.description,
      solution: mistakeForm.solution,
    });

    setMistakes(prev => [...prev, newMistake]);
    setMistakeForm({ subject: "", topic: "", description: "", solution: "" });
    setIsAddingMistake(false);

    toast({
      title: "Success",
      description: "Mistake added successfully",
    });
  };

  const handleAddTakeaway = () => {
    if (!test || !takeawayForm.description) {
      toast({
        title: "Error",
        description: "Please enter a takeaway description",
        variant: "destructive",
      });
      return;
    }

    const newTakeaway = takeawaysStorage.add(test.id, {
      category: takeawayForm.category,
      description: takeawayForm.description,
    });

    setTakeaways(prev => [...prev, newTakeaway]);
    setTakeawayForm({ category: "", description: "" });
    setIsAddingTakeaway(false);

    toast({
      title: "Success",
      description: "Takeaway added successfully",
    });
  };

  const handleDeleteMistake = (mistakeId: string) => {
    mistakesStorage.delete(mistakeId);
    setMistakes(prev => prev.filter(m => m.id !== mistakeId));
    toast({
      title: "Success",
      description: "Mistake deleted successfully",
    });
  };

  const handleDeleteTakeaway = (takeawayId: string) => {
    takeawaysStorage.delete(takeawayId);
    setTakeaways(prev => prev.filter(t => t.id !== takeawayId));
    toast({
      title: "Success",
      description: "Takeaway deleted successfully",
    });
  };

  const toggleReviewStatus = () => {
    if (!test) return;

    const newStatus = !test.isReviewed;
    if (newStatus) {
      enhancedMockTestUtils.markAsReviewed(test.id);
    } else {
      enhancedMockTestUtils.markAsUnreviewed(test.id);
    }

    const updatedTest = { 
      ...test, 
      isReviewed: newStatus,
      reviewedAt: newStatus ? new Date().toISOString() : undefined,
    };
    onTestUpdate?.(updatedTest);

    toast({
      title: "Success",
      description: `Test marked as ${newStatus ? 'reviewed' : 'unreviewed'}`,
    });
  };

  if (!test) return null;

  const percentage = test.marksObtained && test.totalMarks 
    ? (test.marksObtained / test.totalMarks) * 100 
    : 0;

  const getPerformanceColor = (perc: number) => {
    if (perc >= 90) return "text-green-600";
    if (perc >= 75) return "text-blue-600";
    if (perc >= 60) return "text-yellow-600";
    if (perc >= 40) return "text-orange-600";
    return "text-red-600";
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle className="text-xl font-bold">{test.name}</DialogTitle>
            <div className="flex items-center gap-2">
              <Button
                variant={test.isReviewed ? "default" : "outline"}
                size="sm"
                onClick={toggleReviewStatus}
                className="gap-2"
              >
                {test.isReviewed ? (
                  <>
                    <CheckCircle2 className="h-4 w-4" />
                    Reviewed
                  </>
                ) : (
                  <>
                    <AlertCircle className="h-4 w-4" />
                    Mark as Reviewed
                  </>
                )}
              </Button>
              <Button variant="ghost" size="sm" onClick={onClose}>
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </DialogHeader>

        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="syllabus">Syllabus</TabsTrigger>
            <TabsTrigger value="mistakes">Mistakes</TabsTrigger>
            <TabsTrigger value="takeaways">Takeaways</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            {/* Basic Info */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2">
                    <BarChart3 className="h-5 w-5" />
                    Test Overview
                  </CardTitle>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setIsEditing(!isEditing)}
                    className="gap-2"
                  >
                    {isEditing ? (
                      <>
                        <X className="h-4 w-4" />
                        Cancel
                      </>
                    ) : (
                      <>
                        <Edit2 className="h-4 w-4" />
                        Edit
                      </>
                    )}
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">{new Date(test.date).toLocaleDateString()}</span>
                  </div>
                  {test.timeSpent && (
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">{test.timeSpent} min</span>
                    </div>
                  )}
                  {test.difficulty && (
                    <div className="flex items-center gap-2">
                      <Target className="h-4 w-4 text-muted-foreground" />
                      <Badge variant="outline" className="text-xs">
                        {test.difficulty}
                      </Badge>
                    </div>
                  )}
                  {test.testPaperUrl && (
                    <div className="space-y-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => window.open(test.testPaperUrl, '_blank')}
                        className="gap-2 w-full"
                      >
                        <ExternalLink className="h-4 w-4" />
                        View Test Paper ({urlUtils.getFileType(test.testPaperUrl)})
                      </Button>
                      <p className="text-xs text-muted-foreground">
                        Source: {urlUtils.getDomainName(test.testPaperUrl)}
                      </p>
                    </div>
                  )}
                </div>

                {isEditing ? (
                  <div className="space-y-4 p-4 border rounded-lg">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="marks-obtained">Marks Obtained</Label>
                        <Input
                          id="marks-obtained"
                          type="number"
                          value={editForm.marksObtained}
                          onChange={(e) => setEditForm(prev => ({ 
                            ...prev, 
                            marksObtained: parseInt(e.target.value) || 0 
                          }))}
                        />
                      </div>
                      <div>
                        <Label htmlFor="total-marks">Total Marks</Label>
                        <Input
                          id="total-marks"
                          type="number"
                          value={editForm.totalMarks}
                          onChange={(e) => setEditForm(prev => ({ 
                            ...prev, 
                            totalMarks: parseInt(e.target.value) || 0 
                          }))}
                        />
                      </div>
                      <div>
                        <Label htmlFor="time-spent">Time Spent (minutes)</Label>
                        <Input
                          id="time-spent"
                          type="number"
                          value={editForm.timeSpent}
                          onChange={(e) => setEditForm(prev => ({ 
                            ...prev, 
                            timeSpent: parseInt(e.target.value) || 0 
                          }))}
                        />
                      </div>
                      <div>
                        <Label htmlFor="target-score">Target Score</Label>
                        <Input
                          id="target-score"
                          type="number"
                          value={editForm.targetScore}
                          onChange={(e) => setEditForm(prev => ({ 
                            ...prev, 
                            targetScore: parseInt(e.target.value) || 0 
                          }))}
                        />
                      </div>
                    </div>
                    <div>
                      <Label htmlFor="difficulty">Difficulty</Label>
                      <Select
                        value={editForm.difficulty}
                        onValueChange={(value: 'easy' | 'medium' | 'hard') => 
                          setEditForm(prev => ({ ...prev, difficulty: value }))
                        }
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="easy">Easy</SelectItem>
                          <SelectItem value="medium">Medium</SelectItem>
                          <SelectItem value="hard">Hard</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <Button onClick={handleSaveEdit} className="gap-2">
                      <Save className="h-4 w-4" />
                      Save Changes
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {/* Score Display */}
                    {test.marksObtained !== undefined && test.totalMarks !== undefined && (
                      <div className="text-center space-y-2">
                        <div className="text-3xl font-bold">
                          {test.marksObtained}/{test.totalMarks}
                        </div>
                        <div className={cn("text-xl font-semibold", getPerformanceColor(percentage))}>
                          {percentage.toFixed(1)}%
                        </div>
                        <Progress value={percentage} className="h-3 max-w-xs mx-auto" />
                        {test.targetScore && (
                          <div className="text-sm text-muted-foreground">
                            Target: {test.targetScore} 
                            {percentage >= (test.targetScore / test.totalMarks) * 100 && (
                              <Award className="h-4 w-4 inline ml-1 text-yellow-500" />
                            )}
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Quick Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-blue-600">{mistakes.length}</div>
                  <div className="text-sm text-muted-foreground">Mistakes</div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-green-600">{takeaways.length}</div>
                  <div className="text-sm text-muted-foreground">Takeaways</div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-purple-600">
                    {syllabus?.overallProgress || 0}%
                  </div>
                  <div className="text-sm text-muted-foreground">Syllabus</div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-orange-600">
                    {test.isReviewed ? "Yes" : "No"}
                  </div>
                  <div className="text-sm text-muted-foreground">Reviewed</div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="syllabus">
            <SyllabusTracker 
              testId={test.id} 
              testName={test.name}
              onProgressUpdate={(progress) => {
                // Update syllabus state if needed
                if (syllabus) {
                  setSyllabus({ ...syllabus, overallProgress: progress });
                }
              }}
            />
          </TabsContent>

          <TabsContent value="mistakes" className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">Mistakes & Errors</h3>
              <Button
                onClick={() => setIsAddingMistake(true)}
                size="sm"
                className="gap-2"
              >
                <Plus className="h-4 w-4" />
                Add Mistake
              </Button>
            </div>

            {isAddingMistake && (
              <Card>
                <CardContent className="p-4 space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="mistake-subject">Subject</Label>
                      <Input
                        id="mistake-subject"
                        value={mistakeForm.subject}
                        onChange={(e) => setMistakeForm(prev => ({ ...prev, subject: e.target.value }))}
                        placeholder="e.g., Physics, Chemistry"
                      />
                    </div>
                    <div>
                      <Label htmlFor="mistake-topic">Topic</Label>
                      <Input
                        id="mistake-topic"
                        value={mistakeForm.topic}
                        onChange={(e) => setMistakeForm(prev => ({ ...prev, topic: e.target.value }))}
                        placeholder="e.g., Thermodynamics"
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="mistake-description">Description</Label>
                    <Textarea
                      id="mistake-description"
                      value={mistakeForm.description}
                      onChange={(e) => setMistakeForm(prev => ({ ...prev, description: e.target.value }))}
                      placeholder="Describe what went wrong..."
                      rows={3}
                    />
                  </div>
                  <div>
                    <Label htmlFor="mistake-solution">Solution/Learning</Label>
                    <Textarea
                      id="mistake-solution"
                      value={mistakeForm.solution}
                      onChange={(e) => setMistakeForm(prev => ({ ...prev, solution: e.target.value }))}
                      placeholder="How to avoid this mistake in future..."
                      rows={3}
                    />
                  </div>
                  <div className="flex gap-2">
                    <Button onClick={handleAddMistake}>Add Mistake</Button>
                    <Button variant="outline" onClick={() => setIsAddingMistake(false)}>
                      Cancel
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            {mistakes.length === 0 ? (
              <Card>
                <CardContent className="text-center py-8">
                  <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No mistakes recorded</h3>
                  <p className="text-muted-foreground">
                    Add mistakes to track areas for improvement
                  </p>
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-4">
                {mistakes.map((mistake, index) => (
                  <motion.div
                    key={mistake.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                  >
                    <Card>
                      <CardContent className="p-4">
                        <div className="flex items-start justify-between mb-3">
                          <div className="flex items-center gap-2">
                            <Badge variant="outline">{mistake.subject}</Badge>
                            {mistake.topic && (
                              <Badge variant="secondary">{mistake.topic}</Badge>
                            )}
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteMistake(mistake.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                        <p className="text-sm mb-3">{mistake.description}</p>
                        {mistake.solution && (
                          <div className="bg-muted p-3 rounded-lg">
                            <p className="text-sm font-medium mb-1">Solution:</p>
                            <p className="text-sm">{mistake.solution}</p>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  </motion.div>
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="takeaways" className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">Key Takeaways</h3>
              <Button
                onClick={() => setIsAddingTakeaway(true)}
                size="sm"
                className="gap-2"
              >
                <Plus className="h-4 w-4" />
                Add Takeaway
              </Button>
            </div>

            {isAddingTakeaway && (
              <Card>
                <CardContent className="p-4 space-y-4">
                  <div>
                    <Label htmlFor="takeaway-category">Category (Optional)</Label>
                    <Input
                      id="takeaway-category"
                      value={takeawayForm.category}
                      onChange={(e) => setTakeawayForm(prev => ({ ...prev, category: e.target.value }))}
                      placeholder="e.g., Strategy, Time Management"
                    />
                  </div>
                  <div>
                    <Label htmlFor="takeaway-description">Takeaway</Label>
                    <Textarea
                      id="takeaway-description"
                      value={takeawayForm.description}
                      onChange={(e) => setTakeawayForm(prev => ({ ...prev, description: e.target.value }))}
                      placeholder="What did you learn from this test?"
                      rows={3}
                    />
                  </div>
                  <div className="flex gap-2">
                    <Button onClick={handleAddTakeaway}>Add Takeaway</Button>
                    <Button variant="outline" onClick={() => setIsAddingTakeaway(false)}>
                      Cancel
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            {takeaways.length === 0 ? (
              <Card>
                <CardContent className="text-center py-8">
                  <TrendingUp className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No takeaways recorded</h3>
                  <p className="text-muted-foreground">
                    Add key learnings and insights from this test
                  </p>
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-4">
                {takeaways.map((takeaway, index) => (
                  <motion.div
                    key={takeaway.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                  >
                    <Card>
                      <CardContent className="p-4">
                        <div className="flex items-start justify-between mb-3">
                          <div>
                            {takeaway.category && (
                              <Badge variant="outline" className="mb-2">
                                {takeaway.category}
                              </Badge>
                            )}
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteTakeaway(takeaway.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                        <p className="text-sm">{takeaway.description}</p>
                      </CardContent>
                    </Card>
                  </motion.div>
                ))}
              </div>
            )}
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}
