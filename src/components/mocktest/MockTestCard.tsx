import { format } from "date-fns";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Trash,
  Award,
  Calendar as CalendarI<PERSON>,
  BarChart3,
  FileText,
  CheckCircle,
  Clock,
  ExternalLink,
  Eye,
  Target
} from "lucide-react";
import { MockTest, TestCategory } from "@/types/mockTest";
import { <PERSON>, CardContent, CardFooter, CardHeader } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { cn } from "@/lib/utils";

interface MockTestCardProps {
  mockTest: MockTest;
  onEditClick: (mockTest: MockTest) => void;
  onDeleteClick: (mockTestId: string) => void;
  onViewDetails?: (mockTest: MockTest) => void;
  categories?: TestCategory[];
}

export function MockTestCard({
  mockTest,
  onEditClick,
  onDeleteClick,
  onViewDetails,
  categories = []
}: MockTestCardProps) {
  const percentage = mockTest.totalMarks > 0 ? (mockTest.totalMarksObtained / mockTest.totalMarks) * 100 : 0;
  const formattedPercentage = percentage.toFixed(1);

  const getCategoryName = (categoryId?: string) => {
    if (!categoryId) return null;
    return categories.find(cat => cat.id === categoryId)?.name;
  };

  const getGradeColor = (percentage: number) => {
    if (percentage >= 90) return "bg-green-500";
    if (percentage >= 75) return "bg-green-400";
    if (percentage >= 60) return "bg-yellow-400";
    if (percentage >= 40) return "bg-orange-400";
    return "bg-red-500";
  };



  const getTextColor = (percentage: number) => {
    if (percentage >= 90) return "text-green-500";
    if (percentage >= 75) return "text-green-400";
    if (percentage >= 60) return "text-yellow-500";
    if (percentage >= 40) return "text-orange-500";
    return "text-red-500";
  };

  return (
    <Card className="overflow-hidden transition-all duration-300 hover:shadow-lg border border-primary/10 relative group">

      <CardHeader className="flex flex-col space-y-2 p-5 pb-3">
        <div className="flex justify-between items-start w-full">
          <div className="flex-1 mr-3">
            <div className="flex flex-wrap gap-1.5 mb-3">
              {mockTest.subjectMarks.map((subject, index) => (
                <Badge
                  key={index}
                  style={{
                    backgroundColor: `${subject.subjectColor}20`,
                    color: subject.subjectColor,
                    borderColor: `${subject.subjectColor}40`
                  }}
                  className="text-xs font-medium transition-all duration-300 hover:scale-105 border px-2 py-0.5"
                >
                  {subject.subject}
                </Badge>
              ))}
            </div>

            <div className="flex items-center gap-2 mb-2">
              <h3 className="font-semibold text-lg line-clamp-1 group-hover:text-primary transition-colors duration-300">{mockTest.name}</h3>
              {mockTest.isReviewed && (
                <Badge variant="outline" className="text-xs bg-green-50 text-green-700 border-green-200">
                  <CheckCircle className="h-3 w-3 mr-1" />
                  Reviewed
                </Badge>
              )}
            </div>

            <div className="flex items-center gap-3 text-sm text-muted-foreground">
              <div className="flex items-center gap-1.5">
                <CalendarIcon className="h-3.5 w-3.5" />
                {mockTest.date ? format(new Date(mockTest.date), "PP") : "No date"}
              </div>
              {getCategoryName(mockTest.categoryId) && (
                <Badge variant="secondary" className="text-xs">
                  {getCategoryName(mockTest.categoryId)}
                </Badge>
              )}
            </div>
          </div>

          <div className="flex items-center justify-center h-16 w-16 rounded-full bg-primary/10 shadow-sm">
            <span className={`text-lg font-bold ${getTextColor(percentage)}`}>
              {formattedPercentage}%
            </span>
          </div>
        </div>
      </CardHeader>

      <CardContent className="p-5 pt-0">
        <div className="mb-5">
          <div className="flex justify-between items-center mb-2">
            <p className="text-sm font-medium flex items-center gap-1.5">
              <Award className="h-3.5 w-3.5 text-muted-foreground" />
              Overall Performance
            </p>
            <p className={`text-sm font-medium ${getTextColor(percentage)}`}>{formattedPercentage}%</p>
          </div>
          <div className="relative h-2.5 w-full rounded-full bg-muted overflow-hidden">
            <Progress
              value={percentage}
              className={`h-full rounded-full ${getGradeColor(percentage)}`}
            />
          </div>
        </div>

        <div className="space-y-4">
          {mockTest.subjectMarks.map((subject, index) => {
            const subjectPercentage = (subject.marksObtained / subject.totalMarks) * 100;
            return (
              <div key={index} className="space-y-2 group">
                <div className="flex justify-between items-center text-xs">
                  <div className="flex items-center gap-2">
                    <div
                      className="w-3 h-3 rounded-full transition-transform duration-300 group-hover:scale-125"
                      style={{ backgroundColor: subject.subjectColor }}
                    ></div>
                    <span className="font-medium">{subject.subject}</span>
                  </div>
                  <span className="font-medium">
                    {subject.marksObtained}/{subject.totalMarks} ({subjectPercentage.toFixed(1)}%)
                  </span>
                </div>
                <div className="relative h-1.5 w-full rounded-full overflow-hidden"
                     style={{ backgroundColor: `${subject.subjectColor}20` }}>
                  <Progress
                    value={subjectPercentage}
                    className="h-full rounded-full transition-all duration-500 ease-in-out"
                    style={{ backgroundColor: subject.subjectColor }}
                  />
                </div>
              </div>
            );
          })}
        </div>

        <div className="mt-5 pt-4 border-t border-border/30">
          <div className="flex justify-between items-center">
            <p className="text-sm font-medium flex items-center gap-1.5">
              <BarChart3 className="h-3.5 w-3.5 text-muted-foreground" />
              Total Score
            </p>
            <p className="font-medium text-lg">
              {mockTest.totalMarksObtained} / {mockTest.totalMarks}
            </p>
          </div>
        </div>

        {mockTest.notes && (
          <div className="mt-4 pt-4 border-t border-border/30 bg-muted/30 p-4 rounded-lg">
            <p className="text-sm font-medium text-muted-foreground mb-1 flex items-center gap-1.5">
              <FileText className="h-3.5 w-3.5" />
              Notes
            </p>
            <p className="text-sm line-clamp-2">{mockTest.notes}</p>
          </div>
        )}
      </CardContent>

      <CardFooter className="p-5 pt-0 flex justify-between items-center">
        <div className="flex gap-2">
          {onViewDetails && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => onViewDetails(mockTest)}
              className="gap-2"
            >
              <Eye className="h-4 w-4" />
              View Details
            </Button>
          )}
          {mockTest.testPaperUrl && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => window.open(mockTest.testPaperUrl, '_blank')}
              className="gap-2"
            >
              <ExternalLink className="h-4 w-4" />
              Paper
            </Button>
          )}
        </div>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="outline"
              size="icon"
              className="h-8 w-8 rounded-full border-primary/20 hover:bg-primary/5 hover:border-primary/30 transition-all duration-300"
            >
              <MoreHorizontal className="h-4 w-4 text-muted-foreground" />
              <span className="sr-only">Actions</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-48 border border-primary/10">
            <DropdownMenuItem
              onClick={() => onEditClick(mockTest)}
              className="cursor-pointer hover:bg-primary/5 hover:text-primary transition-colors duration-200"
            >
              <Pencil className="h-4 w-4 mr-2" />
              Edit Test
            </DropdownMenuItem>
            <DropdownMenuItem
              className="text-red-600 cursor-pointer hover:bg-red-50 dark:hover:bg-red-950/20 transition-colors duration-200"
              onClick={() => onDeleteClick(mockTest.id)}
            >
              <Trash className="h-4 w-4 mr-2" />
              Delete Test
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </CardFooter>
    </Card>
  );
}